// Designed by Cascade AI to bridge MagicBlend and Animancer.
// Located under KINEMATION/MagicBlend/Runtime/Integration
// Allows you to call animancer.Play(blendAsset, fadeTime) just like any other Animancer transition.

using System.Collections.Generic;
using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using KINEMATION.MagicBlend.Integration;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace KINEMATION.MagicBlend.Integration
{
    /// <summary>
    /// A transition wrapper so a <see cref="MagicBlendAsset"/> can be played through Animancer's API.
    /// Usage (instance method):
    ///     animancer.Play(new MagicBlendTransition { BlendAsset = asset, FadeDuration = 0.3f });
    /// Or simply: animancer.Play(asset, 0.3f) once the <see cref="MagicBlendAnimancerExtensions"/> file is in scope.
    /// </summary>
    [System.Serializable]
    public sealed class MagicBlendTransition : Transition<KINEMATION.MagicBlend.Integration.MagicBlendPlayableState>, IAnimationClipCollection
    {
        [Tooltip("The MagicBlend asset to play via Animancer.")]
        [SerializeField] private MagicBlendAsset _BlendAsset;

        /// <summary>The MagicBlend asset to play.</summary>
        public MagicBlendAsset BlendAsset
        {
            get => _BlendAsset;
            set => _BlendAsset = value;
        }



        public override object Key => _BlendAsset;

        /********************************************************************************************************************/
        #region Transition Implementation
        /********************************************************************************************************************/

        public override MagicBlendPlayableState CreateState()
        {
            // We can't get the AnimancerComponent during state creation reliably, so just create an empty state.
            return new MagicBlendPlayableState();
        }

        public override void Apply(AnimancerState state)
        {
            if (_BlendAsset == null)
            {
                Debug.LogWarning("[MagicBlendTransition] No BlendAsset has been assigned.");
                return;
            }

            var animancer = state.Graph?.Component as HybridAnimancerComponent;
            if (animancer == null)
            {
                Debug.LogError("[MagicBlendTransition] Transition must be played by a HybridAnimancerComponent.");
                return;
            }

            // Validate the blend asset has proper bone chains configured
            if (_BlendAsset.layeredBlends == null || _BlendAsset.layeredBlends.Count == 0)
            {
                Debug.LogWarning($"[MagicBlendTransition] BlendAsset '{_BlendAsset.name}' has no layered blends configured. Upper body may not be affected. Please configure bone chains in the MagicBlendAsset.");
            }
            else
            {
                // Log bone chains for debugging upper body issues
                foreach (var blend in _BlendAsset.layeredBlends)
                {
                    if (blend.layer.elementChain != null && blend.layer.elementChain.Count > 0)
                    {
                        Debug.Log($"[MagicBlendTransition] Blend layer '{blend.layer.chainName}' has {blend.layer.elementChain.Count} bones configured with weights - Base: {blend.baseWeight:F2}, Additive: {blend.additiveWeight:F2}, Local: {blend.localWeight:F2}");

                        // Check for upper body bones
                        bool hasUpperBodyBones = false;
                        foreach (var element in blend.layer.elementChain)
                        {
                            if (element.name.ToLower().Contains("spine") || element.name.ToLower().Contains("chest") ||
                                element.name.ToLower().Contains("shoulder") || element.name.ToLower().Contains("arm") ||
                                element.name.ToLower().Contains("hand") || element.name.ToLower().Contains("head"))
                            {
                                hasUpperBodyBones = true;
                                break;
                            }
                        }

                        if (!hasUpperBodyBones)
                        {
                            Debug.LogWarning($"[MagicBlendTransition] Blend layer '{blend.layer.chainName}' does not appear to contain upper body bones. This may be why upper body is not affected.");
                        }
                    }
                }
            }

            // Ensure MagicBlending component exists and is initialised for Animancer.
            var magicBlend = animancer.GetComponent<MagicBlending>();
            if (magicBlend == null)
            {
                magicBlend = animancer.gameObject.AddComponent<MagicBlending>();
            }

            magicBlend.InitializeForAnimancer(animancer); // Safe to call multiple times.

            // Use proper blending - let Animancer handle fading, MagicBlend handles bone-level blending
            bool useInternalBlending = FadeDuration <= 0f;
            float blendTime = useInternalBlending ? _BlendAsset.blendTime : 0f;

            Debug.Log($"[MagicBlendTransition] Applying BlendAsset '{_BlendAsset.name}' with useInternalBlending: {useInternalBlending}, blendTime: {blendTime}, FadeDuration: {FadeDuration}");
            magicBlend.PlayBlendViaAnimancer(_BlendAsset, useInternalBlending, blendTime);

            // Link the state to the MagicBlend component so Animancer gets the correct playable.
            if (state is MagicBlendPlayableState mbState)
                mbState.Configure(magicBlend);
        }

        #endregion
        /********************************************************************************************************************/

        #region IAnimationClipCollection
        /// <summary>Adds any clips referenced by the asset so they appear in Animancer's inspectors.</summary>
        /// </summary>
        public void GatherAnimationClips(ICollection<AnimationClip> clips)
        {
            if (_BlendAsset == null) return;
            if (_BlendAsset.basePose) clips.Add(_BlendAsset.basePose);
            if (_BlendAsset.overlayPose) clips.Add(_BlendAsset.overlayPose);
            foreach (var overrideData in _BlendAsset.overrideOverlays)
            {
                if (overrideData.overlay) clips.Add(overrideData.overlay);
            }
        }
        #endregion
        /********************************************************************************************************************/
        // Required by Transition<T>. Provide a very large duration since MagicBlend poses don't have a defined length.
        public override float MaximumDuration => float.PositiveInfinity;

        // Clone support for editor previewing and asset references.
        public override Transition<MagicBlendPlayableState> Clone(CloneContext context)
        {
            var clone = new MagicBlendTransition();
            clone._BlendAsset = _BlendAsset;
            clone.FadeDuration = FadeDuration; // inherited property
            return clone;
        }
    }
}
