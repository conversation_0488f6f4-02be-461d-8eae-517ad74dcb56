// Designed by KINEMATION, 2025.

using KINEMATION.KAnimationCore.Runtime.Rig;
using System.Collections.Generic;
using NewAnimancer;
using Unity.Collections;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Experimental.Animations;
using UnityEngine.Playables;

namespace KINEMATION.MagicBlend.Runtime
{
    [HelpURL("https://kinemation.gitbook.io/magic-blend-documentation/")]
    public class MagicBlending : MonoBehaviour
    {
        public Playable OutputPlayable => _playableMixer;
        public PlayableGraph playableGraph;
        public MagicBlendAsset BlendAsset => blendAsset;

        [Tooltip("This asset controls the blending weights.")] [SerializeField]
        private MagicBlendAsset blendAsset;

        public float externalWeight = 1f;

        [Tooltip("Will update weights every frame.")] [SerializeField]
        private bool forceUpdateWeights = true;

        [Tooltip("Will process the Overlay pose. Keep it on most of the time.")] [SerializeField]
        private bool alwaysAnimatePoses = true;

        private const ushort PlayableSortingPriority = 900;

       [SerializeField] private Animator _animator;
       [SerializeField] private KRigComponent _rigComponent;
       [SerializeField] private bool _isAnimancerIntegrationActive = false;

        private AnimationLayerMixerPlayable _playableMixer;
        private NativeArray<BlendStreamAtom> _atoms;

        private PoseJob _poseJob;
        private OverlayJob _overlayJob;
        private LayeringJob _layeringJob;

        private AnimationScriptPlayable _poseJobPlayable;
        private AnimationScriptPlayable _overlayJobPlayable;
        private AnimationScriptPlayable _layeringJobPlayable;

        private bool _isInitialized;
        private float _blendPlayback = 1f;
        private float _blendTime = 0f;
        private AnimationCurve _blendCurve;

        private MagicBlendAsset _desiredBlendAsset;
        private float _desiredBlendTime;
        private bool _useBlendCurve;
        private List<int> _blendedIndexes = new List<int>();

        private Dictionary<string, int> _hierarchyMap;
        private RuntimeAnimatorController _cachedController;
        private AnimationPlayableOutput _magicBlendOutput;

        private bool _forceBlendOut;
        private bool _wasAnimatorActive;
        private bool _isInitializedForAnimancer = false;

        /// <summary>
        /// Sets a new blending asset.
        /// </summary>
        /// <param name="newAsset">Blending asset.</param>
        /// <param name="useBlending">Whether we need blending.</param>
        /// <param name="blendTime">Blending time in seconds.</param>
        /// <param name="useCurve">Whether we need curve or linear transition.</param>
        public void UpdateMagicBlendAsset(MagicBlendAsset newAsset, bool useBlending = false, float blendTime = -1f,
            bool useCurve = false)
        {
            if (newAsset == null || !_isInitialized)
            {
                Debug.LogWarning("MagicBlending: input asset is NULL!");
                return;
            }

            _desiredBlendAsset = newAsset;
            _useBlendCurve = useCurve;
            _desiredBlendTime = blendTime;

            if (!useBlending)
            {
                _layeringJob.blendWeight = 1f;
                _layeringJobPlayable.SetJobData(_layeringJob);

                SetNewAsset();

                if (!alwaysAnimatePoses)
                {
                    _poseJob.readPose = true;
                    _overlayJob.cachePose = true;

                    _poseJobPlayable.SetJobData(_poseJob);
                    _overlayJobPlayable.SetJobData(_overlayJob);
                }

                return;
            }

            _layeringJob.cachePose = true;
            _layeringJobPlayable.SetJobData(_layeringJob);
        }
        public void StopMagicBlending()
        {
            _forceBlendOut = true;
            _blendPlayback = 0f;
        }

        public void SetOverlayTime(float newTime)
        {
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (!overlayPlayable.IsValid()) return;
            overlayPlayable.SetTime(newTime);
        }

        public float GetOverlayTime(bool isNormalized = true)
        {
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (!overlayPlayable.IsValid() || !blendAsset.isAnimation)
            {
                return 0f;
            }

            float length = (float)overlayPlayable.GetDuration();
            if (Mathf.Approximately(length, 0f))
            {
                return 0f;
            }

            float time = (float)overlayPlayable.GetTime();
            return isNormalized ? Mathf.Clamp01(time / length) : time;
        }

        protected virtual void SetProcessJobs(bool isActive)
        {
            _poseJobPlayable.SetProcessInputs(isActive);
            _overlayJobPlayable.SetProcessInputs(isActive);
        }

        protected virtual void SetNewAsset()
        {
            blendAsset = _desiredBlendAsset;
            _blendCurve = _useBlendCurve ? blendAsset.blendCurve : null;
            _blendTime = _desiredBlendTime > 0f ? _desiredBlendTime : blendAsset.blendTime;

            MagicBlendLibrary.ConnectPose(_poseJobPlayable, playableGraph, blendAsset.basePose);

            float speed = blendAsset.isAnimation ? blendAsset.overlaySpeed : 0f;
            if (blendAsset.HasOverrides())
            {
                MagicBlendLibrary.ConnectOverlays(_overlayJobPlayable, playableGraph, blendAsset.overlayPose,
                    blendAsset.overrideOverlays, speed);
            }
            else
            {
                MagicBlendLibrary.ConnectPose(_overlayJobPlayable, playableGraph, blendAsset.overlayPose, speed);
            }

            // Reset all weights.
            for (int i = 0; i < _hierarchyMap.Count; i++)
            {
                var atom = _atoms[i];
                atom.baseWeight = atom.additiveWeight = atom.localWeight = 0f;
                _atoms[i] = atom;
            }

            // Add indexes which will be blended.
            _blendedIndexes.Clear();
            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var element in blend.layer.elementChain)
                {
                    _hierarchyMap.TryGetValue(element.name, out int index);
                    _blendedIndexes.Add(index);
                }
            }

            // Active the jobs processing.
            SetProcessJobs(true);
            _forceBlendOut = false;

            // Update weights.
            UpdateBlendWeights();
        }

        protected virtual void BuildMagicMixer()
        {
            if (!_playableMixer.IsValid())
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(playableGraph, 3);
                InitializeJobs();
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f);
            }

            if (!_magicBlendOutput.IsOutputValid())
            {
                _magicBlendOutput = AnimationPlayableOutput.Create(playableGraph, "MagicBlendOutput", _animator);
            }
            _magicBlendOutput.SetSourcePlayable(_playableMixer);
            _magicBlendOutput.SetSortingOrder(PlayableSortingPriority > 0 ? PlayableSortingPriority : 100);

            int num = playableGraph.GetOutputCount();
            int animatorPlayableIndex = 0;

            for (int i = 0; i < num; i++)
            {
                var sourcePlayable = playableGraph.GetOutput(i).GetSourcePlayable();
                if (sourcePlayable.GetPlayableType() != typeof(AnimatorControllerPlayable))
                {
                    continue;
                }

                animatorPlayableIndex = i;
            }

            var animatorOutput = playableGraph.GetOutput(animatorPlayableIndex);
            var animatorPlayable = animatorOutput.GetSourcePlayable();

            if (_layeringJobPlayable.IsValid())
            {
                _layeringJobPlayable.DisconnectInput(0);
            }

            _layeringJobPlayable.ConnectInput(0, animatorPlayable, 0, 1f);

            if (blendAsset != null)
            {
                UpdateMagicBlendAsset(blendAsset);
            }
        }

        protected virtual void InitializeMagicBlending()
        {
            playableGraph = _animator.playableGraph;
            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);

            _magicBlendOutput = AnimationPlayableOutput.Create(playableGraph, "MagicBlendOutput", _animator);
            _isInitialized = true;
            BuildMagicMixer();

            playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            playableGraph.Play();
        }

        private void InitializeJobs()
        {
            var rootSceneHandle = _animator.BindSceneTransform(_animator.transform);

            _poseJob = new PoseJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                alwaysAnimate = alwaysAnimatePoses,
                readPose = false
            };
            _poseJobPlayable = AnimationScriptPlayable.Create(playableGraph, _poseJob, 1);

            _overlayJob = new OverlayJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                alwaysAnimate = alwaysAnimatePoses,
                cachePose = false
            };
            _overlayJobPlayable = AnimationScriptPlayable.Create(playableGraph, _overlayJob, 1);

            _layeringJob = new LayeringJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                blendWeight = 1f,
                cachePose = false,
            };
            _layeringJobPlayable = AnimationScriptPlayable.Create(playableGraph, _layeringJob, 1);
        }

        private void OnEnable()
        {
            if (_isInitialized) BuildMagicMixer();
        }

        protected virtual void Awake()
        {
            if (TryGetComponent<HybridAnimancerComponent>(out _))
            {
                _isAnimancerIntegrationActive = true;
                // Debug.Log("[MagicBlending] Awake: Animancer integration is active.");
            }
            else
            {
                // Debug.Log("[MagicBlending] Awake: Animancer integration is NOT active.");
            }
        }

        public void InitializeForAnimancer(HybridAnimancerComponent animancer)
        {
            if (_isInitialized)
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer called, but already initialized. Skipping.");
                return;
            }

            _animator = GetComponent<Animator>();
            if (_rigComponent == null) _rigComponent = GetComponentInChildren<KRigComponent>();

            if (_animator == null || _rigComponent == null)
            {
                Debug.LogError("[MagicBlending] Animator or KRigComponent not found. Initialization failed for Animancer.");
                return;
            }
            Debug.Log("[MagicBlending] InitializeForAnimancer: Animator and KRigComponent found.");

            // Validate and setup bone hierarchy mapping
            _hierarchyMap = new Dictionary<string, int>();
            var hierarchy = _rigComponent.GetRigTransforms();
            for (int i = 0; i < hierarchy.Length; i++)
            {
                if (hierarchy[i] == null) continue;
                _hierarchyMap.Add(hierarchy[i].name, i);
            }

            this.playableGraph = animancer.playableGraph;
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Using Animancer's PlayableGraph. IsValid: {this.playableGraph.IsValid()}");

            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);

            InitializeJobs();
            _isInitializedForAnimancer = true;
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Jobs initialized. _poseJobPlayable.IsValid: {_poseJobPlayable.IsValid()}, _overlayJobPlayable.IsValid: {_overlayJobPlayable.IsValid()}, _layeringJobPlayable.IsValid: {_layeringJobPlayable.IsValid()}");

            // Create the proper 3-input mixer for MagicBlend integration with Animancer
            if (!_playableMixer.IsValid())
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(this.playableGraph, 3);
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f);
                Debug.Log($"[MagicBlending] InitializeForAnimancer: PlayableMixer created and inputs connected. IsValid: {_playableMixer.IsValid()}");
            }
            else
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer: _playableMixer was already valid.");
            }

            // Connect Animancer's base layer to the LayeringJob for proper integration
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Attempting to connect LayeringJob. Animancer layer count: {animancer.Layers.Count}");
            if (animancer.Layers.Count > 0)
            {
                var baseLayerPlayable = animancer.Layers[0].Playable;
                Debug.Log($"[MagicBlending] Animancer baseLayerPlayable.IsValid(): {baseLayerPlayable.IsValid()}, _layeringJobPlayable.IsValid(): {_layeringJobPlayable.IsValid()}");

                if (baseLayerPlayable.IsValid() && _layeringJobPlayable.IsValid())
                {
                    // Disconnect any existing input to avoid conflicts
                    if (_layeringJobPlayable.GetInputCount() > 0 && _layeringJobPlayable.GetInput(0).IsValid())
                    {
                        Debug.LogWarning("[MagicBlending] LayeringJob already has a valid input. Disconnecting before reconnecting.");
                        playableGraph.Disconnect(_layeringJobPlayable, 0);
                    }

                    // Connect Animancer's base layer to LayeringJob input
                    playableGraph.Connect(baseLayerPlayable, 0, _layeringJobPlayable, 0);
                    _layeringJobPlayable.SetInputWeight(0, 1f);
                    Debug.Log("[MagicBlending] Connected Animancer base layer to LayeringJob input successfully.");
                }
                else
                {
                    Debug.LogWarning("[MagicBlending] Animancer base layer playable or LayeringJobPlayable is invalid. Cannot connect for LayeringJob input.");
                }
            }
            else
            {
                Debug.LogWarning("[MagicBlending] Animancer has no layers. Cannot connect base layer to LayeringJob input.");
            }

            // Process pre-assigned blend asset if available
            if (blendAsset != null)
            {
                Debug.Log($"[MagicBlending] InitializeForAnimancer: Processing pre-assigned blendAsset: {blendAsset.name}");
                UpdateMagicBlendAsset(blendAsset);
            }
            else
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer: No blendAsset pre-assigned. The 'input asset is NULL!' warning might appear from UpdateMagicBlendAsset if called later without an asset.");
            }

            this.playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            _isInitialized = true;

            Debug.Log("[MagicBlending] Initialized for Animancer. _isInitialized=true, _isInitializedForAnimancer=true");
        }

        private void Start()
        {
            if (_isAnimancerIntegrationActive)
            {
                // InitializeForAnimancer should have been called by LayeredAnimationManager.Awake().
                if (!_isInitialized)
                {
                    Debug.LogError("[MagicBlending] Start: Animancer integration is active, but component is not initialized. This indicates InitializeForAnimancer was not successfully called by LayeredAnimationManager or failed. Check LayeredAnimationManager setup, script execution order, and previous logs. Attempting fallback call to InitializeForAnimancer.");
                    if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponent))
                    {
                         InitializeForAnimancer(animancerComponent);
                    }
                    else
                    {
                        Debug.LogError("[MagicBlending] Start: Fallback failed. AnimancerComponent not found for InitializeForAnimancer call.");
                    }
                }
            }
            else // Not Animancer integration
            {
                // OnEnable should have initialized for non-Animancer path.
                // This is a fallback if OnEnable didn't run or complete.
                if (!_isInitialized)
                {
                    Debug.LogWarning("[MagicBlending] Start: Fallback non-Animancer initialization. OnEnable might not have completed its setup.");
                    InitializeMagicBlending();
                    BuildMagicMixer();
                }
            }

            // This logic was for handling blendAsset if not processed during initialization.
            // InitializeForAnimancer now handles its own blendAsset processing.
            // The non-Animancer path (InitializeMagicBlending -> BuildMagicMixer) also processes its asset.
            // The "input asset is NULL!" warning comes from UpdateMagicBlendAsset if it's called with a null asset.
            // If blendAsset is meant to be assigned and used at Start for non-Animancer, ensure it's handled.
            if (blendAsset == null && !_isAnimancerIntegrationActive && _isInitialized)
            {
                // This warning is from the original script, let's keep its spirit for non-Animancer.
                // However, UpdateMagicBlendAsset is called within InitializeMagicBlending if blendAsset is not null.
                // So this specific log might be redundant if blendAsset is consistently null at this point for this path.
                // Debug.LogWarning("[MagicBlending] Start: blendAsset is NULL for non-Animancer setup, and was not processed during InitializeMagicBlending.");
            }
        
            // Try to get Animancer component first.
            if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponentInstance))
            {
                // If Animancer is present, initialize for it.
                InitializeForAnimancer(animancerComponentInstance);
            }
            else
            {
                // Original Start() logic for standard Animator
                _animator = GetComponent<Animator>();
                _cachedController = _animator.runtimeAnimatorController;
                _wasAnimatorActive = _animator.isActiveAndEnabled;

                _rigComponent = GetComponentInChildren<KRigComponent>();
                _hierarchyMap = new Dictionary<string, int>();

                var hierarchy = _rigComponent.GetRigTransforms();
                for (int i = 0; i < hierarchy.Length; i++)
                {
                    _hierarchyMap.Add(hierarchy[i].name, i);
                }

                InitializeMagicBlending();

            if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponent))
            {
                InitializeForAnimancer(animancerComponent);
                // Fallback to original initialization if Animancer is not found
                if (!_isInitialized) // Prevent re-initialization if already done by InitializeForAnimancer
                if (Application.isPlaying)
                {
                    if (_isAnimancerIntegrationActive)
                    {
                        // If Animancer is present, initialization is handled by LayeredAnimationManager
                        // calling InitializeForAnimancer. Do nothing here to avoid conflict.
                        return;
                    }

                    // Standard non-Animancer initialization path
                    if (!_isInitialized)
                    {
                        InitializeMagicBlending(); // Sets up its own playableGraph
                        BuildMagicMixer();         // Uses the graph from InitializeMagicBlending
                    }
                }
            }

            // If standalone and asset exists, ensure it's processed.
            // If Animancer-init, UpdateMagicBlendAsset is called within InitializeForAnimancer if blendAsset is present.
            if (blendAsset != null && !_isInitializedForAnimancer && _isInitialized)
            {
                UpdateMagicBlendAsset(blendAsset);
            }

#if UNITY_EDITOR
            _cachedBlendAsset = blendAsset;
#endif
            }
        }

        protected virtual void UpdateBlendWeights(float globalWeight = 1f)
        {
            if (blendAsset == null || _blendedIndexes == null || _atoms.Length == 0)
            {
                Debug.LogWarning("[MagicBlending] UpdateBlendWeights: Invalid state - blendAsset, blendedIndexes, or atoms not properly initialized.");
                return;
            }

            int index = 0;

            // Apply weights to each bone in the layered blends
            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var element in blend.layer.elementChain)
                {
                    if (index >= _blendedIndexes.Count)
                    {
                        Debug.LogWarning($"[MagicBlending] UpdateBlendWeights: Index {index} exceeds blendedIndexes count {_blendedIndexes.Count}. Skipping remaining bones.");
                        break;
                    }

                    int realIndex = _blendedIndexes[index];

                    if (realIndex >= 0 && realIndex < _atoms.Length)
                    {
                        var atom = _atoms[realIndex];

                        // Apply weights with proper scaling for upper body blending
                        float finalGlobalWeight = blendAsset.globalWeight * globalWeight * externalWeight;
                        atom.baseWeight = blend.baseWeight * finalGlobalWeight;
                        atom.additiveWeight = blend.additiveWeight * finalGlobalWeight;
                        atom.localWeight = blend.localWeight * finalGlobalWeight;

                        _atoms[realIndex] = atom;

                        // Debug log for upper body bones to verify they're being processed
                        if (element.name.ToLower().Contains("spine") || element.name.ToLower().Contains("chest") ||
                            element.name.ToLower().Contains("shoulder") || element.name.ToLower().Contains("arm") ||
                            element.name.ToLower().Contains("hand") || element.name.ToLower().Contains("head"))
                        {
                            Debug.Log($"[MagicBlending] UpdateBlendWeights: Upper body bone '{element.name}' (index {realIndex}) - baseWeight: {atom.baseWeight:F3}, additiveWeight: {atom.additiveWeight:F3}, localWeight: {atom.localWeight:F3}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"[MagicBlending] UpdateBlendWeights: Invalid realIndex {realIndex} for bone '{element.name}'. Atoms length: {_atoms.Length}");
                    }

                    index++;
                }
            }

            // Update overlay weights for override overlays
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (overlayPlayable.IsValid())
            {
                int count = overlayPlayable.GetInputCount();
                if (count > 1 && blendAsset.overrideOverlays != null)
                {
                    for (int i = 1; i < count && i - 1 < blendAsset.overrideOverlays.Count; i++)
                    {
                        float overlayWeight = blendAsset.overrideOverlays[i - 1].weight * globalWeight;
                        overlayPlayable.SetInputWeight(i, overlayWeight);
                    }
                }
            }
        }

        protected virtual void Update()
        {
            if (blendAsset == null) return;

            if (!_isInitializedForAnimancer)
            {
                var activeAnimator = _animator.runtimeAnimatorController;
                if (_cachedController != activeAnimator || _wasAnimatorActive != _animator.isActiveAndEnabled)
                {
                    BuildMagicMixer();
                }
                _cachedController = activeAnimator;
                _wasAnimatorActive = _animator.isActiveAndEnabled;
            }

            if (blendAsset.isAnimation)
            {
                var overlayPlayableInput = _overlayJobPlayable.GetInput(0);
                int count = overlayPlayableInput.GetInputCount();

                var overlayPlayable = count == 0 ? overlayPlayableInput : overlayPlayableInput.GetInput(0);

                if (blendAsset.overlayPose.isLooping && overlayPlayable.GetTime() >= blendAsset.overlayPose.length)
                {
                    overlayPlayable.SetTime(0f);
                }

                if (count > 1)
                {
                    for (int i = 1; i < count; i++)
                    {
                        var overrideOverlay = overlayPlayableInput.GetInput(i);
                        var overrideClip = blendAsset.overrideOverlays[i - 1].overlay;

                        if (!overrideClip.isLooping || overrideOverlay.GetTime() < overrideClip.length)
                        {
                            continue;
                        }

                        overrideOverlay.SetTime(0f);
                    }
                }
            }

            float globalWeight = 1f;
            if (_forceBlendOut)
            {
                _blendPlayback = Mathf.Clamp(_blendPlayback + Time.deltaTime, 0f, _blendTime);
                float blendOutWeight = _blendPlayback / _blendTime;
                globalWeight = 1f - (_blendCurve?.Evaluate(blendOutWeight) ?? blendOutWeight);
            }

            if (forceUpdateWeights || _forceBlendOut)
            {
                UpdateBlendWeights(globalWeight * externalWeight);
            }

            if (Mathf.Approximately(globalWeight, 0f))
            {
                SetProcessJobs(false);
                blendAsset = null;
#if UNITY_EDITOR
                _cachedBlendAsset = null;
#endif
            }

            if (_forceBlendOut || Mathf.Approximately(_blendTime, 0f)
                               || Mathf.Approximately(_blendPlayback, _blendTime)) return;

            _blendPlayback = Mathf.Clamp(_blendPlayback + Time.deltaTime, 0f, _blendTime);
            float normalizedWeight = _blendPlayback / _blendTime;

            _layeringJob.blendWeight = _blendCurve?.Evaluate(normalizedWeight) ?? normalizedWeight;
            _layeringJobPlayable.SetJobData(_layeringJob);
        }

        protected virtual void LateUpdate()
        {
            if (!alwaysAnimatePoses && _poseJob.readPose)
            {
                _poseJob.readPose = false;
                _overlayJob.cachePose = false;

                _poseJobPlayable.SetJobData(_poseJob);
                _overlayJobPlayable.SetJobData(_overlayJob);
            }

            if (_layeringJob.cachePose)
            {
                SetNewAsset();

                _blendPlayback = 0f;

                _layeringJob.cachePose = false;
                _layeringJob.blendWeight = 0f;
                _layeringJobPlayable.SetJobData(_layeringJob);

                if (!alwaysAnimatePoses)
                {
                    _poseJob.readPose = true;
                    _overlayJob.cachePose = true;

                    _poseJobPlayable.SetJobData(_poseJob);
                    _overlayJobPlayable.SetJobData(_overlayJob);
                }
            }
        }

        protected virtual void OnDestroy()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
            {
                playableGraph.Stop();
            }

            if (_atoms.IsCreated)
            {
                _atoms.Dispose();
            }
        }

        public void SetMagicBlendAsset(MagicBlendAsset newAsset)
        {
            blendAsset = newAsset;
        }

        /// <summary>
        /// Convenience wrapper so Animancer integrations can trigger MagicBlend with a single call.
        /// Internally it just forwards to <see cref="UpdateMagicBlendAsset"/>.
        /// </summary>
        /// <param name="asset">Blend asset to play.</param>
        /// <param name="useBlending">True = let MagicBlend blend from current asset, false = switch immediately.</param>
        /// <param name="blendTime">Custom blend time (seconds). If negative MagicBlendAsset.blendTime is used.</param>
        public void PlayBlendViaAnimancer(MagicBlendAsset asset, bool useBlending = false, float blendTime = -1f)
        {
            UpdateMagicBlendAsset(asset, useBlending, blendTime);
        }

#if UNITY_EDITOR
        private MagicBlendAsset _cachedBlendAsset;

        private void OnValidate()
        {
            if (!_isInitialized)
            {
                return;
            }

            _poseJob.alwaysAnimate = alwaysAnimatePoses;
            _overlayJob.alwaysAnimate = alwaysAnimatePoses;

            _poseJobPlayable.SetJobData(_poseJob);
            _overlayJobPlayable.SetJobData(_overlayJob);

            if (_cachedBlendAsset == blendAsset)
            {
                return;
            }

            UpdateMagicBlendAsset(blendAsset, true, 0f, true);
            (_cachedBlendAsset, blendAsset) = (blendAsset, _cachedBlendAsset);
        }
#endif
    }
}