// Auto-generated by <PERSON>: wraps a MagicBlending output so <PERSON><PERSON><PERSON> can treat it like a normal state.

using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using UnityEngine.Playables;
using UnityEngine;

namespace KINEMATION.MagicBlend.Integration
{
    /// <summary>
    /// A minimal <see cref="AnimancerState"/> implementation that simply feeds the playable produced by a
    /// <see cref="MagicBlending"/> component into the Animancer graph.
    /// </summary>
    public sealed class MagicBlendPlayableState : AnimancerState
    {
        private MagicBlending _magicBlend;
        private float _cachedLength = 1f;

        public MagicBlendPlayableState() { }

        public MagicBlendPlayableState(MagicBlending magicBlend)
        {
            Configure(magicBlend);
        }

        /// <summary>Assigns the <see cref="MagicBlending"/> component to wrap.</summary>
        public void Configure(MagicBlending magicBlend)
        {
            _magicBlend = magicBlend;
        }

        public override float Length => _cachedLength;

        public override bool IsLooping => true;

        protected override void CreatePlayable(out Playable playable)
        {
            if (_magicBlend == null)
            {
                // Attempt lazy resolution using the graph's component (HybridAnimancerComponent).
                var animancer = Graph?.Component as HybridAnimancerComponent;
                if (animancer != null)
                {
                    _magicBlend = animancer.GetComponent<MagicBlending>() ?? animancer.gameObject.AddComponent<MagicBlending>();

                    // Initialize MagicBlending for Animancer if not already done
                    if (!_magicBlend.playableGraph.IsValid())
                    {
                        _magicBlend.InitializeForAnimancer(animancer);
                        Debug.Log("[MagicBlendPlayableState] Initialized MagicBlending for Animancer integration.");
                    }
                }
                else
                {
                    Debug.LogError("[MagicBlendPlayableState] Could not find HybridAnimancerComponent for MagicBlend integration.");
                }
            }

            // Use MagicBlending's output playable, which contains the properly mixed result
            if (_magicBlend != null && _magicBlend.OutputPlayable.IsValid())
            {
                playable = _magicBlend.OutputPlayable;
                Debug.Log($"[MagicBlendPlayableState] Using MagicBlending OutputPlayable. IsValid: {playable.IsValid()}");
            }
            else
            {
                // Fallback to null playable if MagicBlending is not available
                playable = Playable.Null;
                Debug.LogWarning("[MagicBlendPlayableState] MagicBlending OutputPlayable is invalid, using Null playable.");
            }
        }

        /********************************************************************************************************************/
        // Clone support (required by AnimancerState abstract implementation).
        public override AnimancerState Clone(CloneContext context)
        {
            var clone = new MagicBlendPlayableState();
            // We generally don't clone runtime component references.
            clone._cachedLength = _cachedLength;
            return clone;
        }
    }
}
