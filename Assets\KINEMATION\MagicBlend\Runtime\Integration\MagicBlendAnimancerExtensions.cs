// Extension methods so users can simply call Animancer.Play(MagicBlendAsset, fadeTime)
// without manually constructing a MagicBlendTransition.

using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using KINEMATION.KAnimationCore.Runtime.Rig;
using UnityEngine;

namespace KINEMATION.MagicBlend.Integration
{
    public static class MagicBlendAnimancerExtensions
    {
        /// <summary>
        /// Plays the given <paramref name="asset"/> through Animan<PERSON>, creating a temporary <see cref="MagicBlendTransition"/>.
        /// This method validates the bone chains and provides helpful debugging information.
        /// </summary>
        public static AnimancerState Play(this HybridAnimancerComponent animancer, MagicBlendAsset asset, float fadeDuration = 0.25f)
        {
            if (animancer == null)
            {
                Debug.LogError("[MagicBlendAnimancerExtensions] HybridAnimancerComponent is null.");
                return null;
            }

            if (asset == null)
            {
                Debug.LogError("[MagicBlendAnimancerExtensions] MagicBlendAsset is null.");
                return null;
            }

            // Validate bone chains before playing
            var rigComponent = animancer.GetComponentInChildren<KRigComponent>();
            if (rigComponent != null)
            {
                MagicBlendBoneChainValidator.ValidateBoneChains(asset, rigComponent);
            }
            else
            {
                Debug.LogWarning("[MagicBlendAnimancerExtensions] No KRigComponent found. Cannot validate bone chains.");
            }

            var transition = new MagicBlendTransition
            {
                BlendAsset = asset,
                FadeDuration = fadeDuration
            };

            Debug.Log($"[MagicBlendAnimancerExtensions] Playing MagicBlendAsset '{asset.name}' with fade duration {fadeDuration}s");
            return animancer.Play(transition);
        }

        /// <summary>
        /// Tries to play a MagicBlendAsset if it exists in the state dictionary, otherwise creates a new state.
        /// </summary>
        public static AnimancerState TryPlay(this HybridAnimancerComponent animancer, MagicBlendAsset asset, float fadeDuration = 0.25f)
        {
            if (animancer == null || asset == null) return null;

            // Create a transition and try to get existing state using the transition as key
            var transition = new MagicBlendTransition
            {
                BlendAsset = asset,
                FadeDuration = fadeDuration
            };

            // Try to get existing state using the asset as key (if it exists)
            var existingState = animancer.States.GetOrCreate(transition);
            if (existingState != null && existingState.IsPlaying)
            {
                // If already playing, just adjust the weight
                return animancer.Play(existingState, fadeDuration);
            }

            // Fall back to creating new state
            return animancer.Play(asset, fadeDuration);
        }

        /// <summary>
        /// Stops a MagicBlendAsset if it's currently playing.
        /// </summary>
        public static AnimancerState Stop(this HybridAnimancerComponent animancer, MagicBlendAsset asset)
        {
            if (animancer == null || asset == null) return null;

            // Create a transition to use as key for finding the state
            var transition = new MagicBlendTransition
            {
                BlendAsset = asset
            };

            var state = animancer.States.GetOrCreate(transition);
            if (state != null && state.IsPlaying)
            {
                state.Stop();
                Debug.Log($"[MagicBlendAnimancerExtensions] Stopped MagicBlendAsset '{asset.name}'");
            }

            return state;
        }

        /// <summary>
        /// Validates and optionally fixes bone chain issues in a MagicBlendAsset.
        /// </summary>
        public static bool ValidateAndFixBoneChains(this HybridAnimancerComponent animancer, MagicBlendAsset asset, bool autoFix = false)
        {
            if (animancer == null || asset == null) return false;

            var rigComponent = animancer.GetComponentInChildren<KRigComponent>();
            if (rigComponent == null)
            {
                Debug.LogError("[MagicBlendAnimancerExtensions] No KRigComponent found for bone chain validation.");
                return false;
            }

            bool isValid = MagicBlendBoneChainValidator.ValidateBoneChains(asset, rigComponent);

            if (!isValid && autoFix)
            {
                Debug.Log($"[MagicBlendAnimancerExtensions] Attempting to auto-fix bone chains for '{asset.name}'");
                MagicBlendBoneChainValidator.AutoFixBoneChains(asset, rigComponent);
            }

            return isValid;
        }
    }
}
