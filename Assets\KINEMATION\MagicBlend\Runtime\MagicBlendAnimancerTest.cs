// Simple test script to verify MagicBlend + Animancer integration works correctly
// Attach this to a GameObject with HybridAnimancerComponent to test the integration

using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using KINEMATION.MagicBlend.Integration;
using KINEMATION.KAnimationCore.Runtime.Rig;
using UnityEngine;

namespace KINEMATION.MagicBlend.Testing
{
    /// <summary>
    /// Simple test script to verify MagicBlend + Animancer integration.
    /// This demonstrates the fixed integration and validates that upper body blending works.
    /// </summary>
    public class MagicBlendAnimancerTest : MonoBehaviour
    {
        [Header("Test Assets")]
        [SerializeField] private MagicBlendAsset testBlendAsset;
        
        [Header("Test Settings")]
        [SerializeField] private float fadeTime = 0.25f;
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private bool validateBoneChains = true;

        private HybridAnimancerComponent animancer;
        private KRigComponent rigComponent;

        private void Start()
        {
            // Get required components
            animancer = GetComponent<HybridAnimancerComponent>();
            rigComponent = GetComponentInChildren<KRigComponent>();

            if (animancer == null)
            {
                Debug.LogError("[MagicBlendTest] HybridAnimancerComponent not found! Please add one to this GameObject.");
                return;
            }

            if (rigComponent == null)
            {
                Debug.LogError("[MagicBlendTest] KRigComponent not found! Please ensure the character has a properly configured rig.");
                return;
            }

            if (runTestOnStart && testBlendAsset != null)
            {
                RunIntegrationTest();
            }
        }

        [ContextMenu("Run Integration Test")]
        public void RunIntegrationTest()
        {
            if (testBlendAsset == null)
            {
                Debug.LogError("[MagicBlendTest] No test blend asset assigned!");
                return;
            }

            Debug.Log("=== MagicBlend + Animancer Integration Test ===");

            // Step 1: Validate bone chains
            if (validateBoneChains)
            {
                Debug.Log("[MagicBlendTest] Step 1: Validating bone chains...");
                bool isValid = MagicBlendBoneChainValidator.ValidateBoneChains(testBlendAsset, rigComponent);
                
                if (!isValid)
                {
                    Debug.LogWarning("[MagicBlendTest] Bone chain validation failed. Attempting auto-fix...");
                    bool _fixed = MagicBlendBoneChainValidator.AutoFixBoneChains(testBlendAsset, rigComponent);
                    
                    if (_fixed)
                    {
                        Debug.Log("[MagicBlendTest] Auto-fix applied successfully!");
                    }
                    else
                    {
                        Debug.LogWarning("[MagicBlendTest] Auto-fix could not resolve all issues.");
                    }
                }
                else
                {
                    Debug.Log("[MagicBlendTest] Bone chain validation passed!");
                }
            }

            // Step 2: Test the integration
            Debug.Log("[MagicBlendTest] Step 2: Testing Animancer.Play(MagicBlendAsset, fadeTime)...");
            
            try
            {
                var state = animancer.Play(testBlendAsset, fadeTime);
                
                if (state != null)
                {
                    Debug.Log($"[MagicBlendTest] ✅ SUCCESS! MagicBlendAsset played successfully. State: {state}");
                    Debug.Log($"[MagicBlendTest] State Type: {state.GetType().Name}");
                    Debug.Log($"[MagicBlendTest] State IsPlaying: {state.IsPlaying}");
                    Debug.Log($"[MagicBlendTest] State Weight: {state.Weight:F3}");
                }
                else
                {
                    Debug.LogError("[MagicBlendTest] ❌ FAILED! Play() returned null state.");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[MagicBlendTest] ❌ EXCEPTION during Play(): {e.Message}");
                Debug.LogException(e);
            }

            // Step 3: Verify MagicBlending component
            Debug.Log("[MagicBlendTest] Step 3: Verifying MagicBlending component...");
            var magicBlending = GetComponent<MagicBlending>();
            
            if (magicBlending != null)
            {
                Debug.Log($"[MagicBlendTest] MagicBlending found. PlayableGraph valid: {magicBlending.playableGraph.IsValid()}");
                Debug.Log($"[MagicBlendTest] Current BlendAsset: {(magicBlending.BlendAsset ? magicBlending.BlendAsset.name : "None")}");
            }
            else
            {
                Debug.LogWarning("[MagicBlendTest] MagicBlending component not found. It should be auto-created during Play().");
            }

            Debug.Log("=== Integration Test Complete ===");
        }

        [ContextMenu("Test TryPlay")]
        public void TestTryPlay()
        {
            if (testBlendAsset == null || animancer == null) return;

            Debug.Log("[MagicBlendTest] Testing TryPlay()...");
            var state = animancer.TryPlay(testBlendAsset, fadeTime);
            
            if (state != null)
            {
                Debug.Log($"[MagicBlendTest] TryPlay() successful. State: {state}");
            }
            else
            {
                Debug.LogError("[MagicBlendTest] TryPlay() failed.");
            }
        }

        [ContextMenu("Test Stop")]
        public void TestStop()
        {
            if (testBlendAsset == null || animancer == null) return;

            Debug.Log("[MagicBlendTest] Testing Stop()...");
            var state = animancer.Stop(testBlendAsset);
            
            if (state != null)
            {
                Debug.Log($"[MagicBlendTest] Stop() successful. State: {state}, IsPlaying: {state.IsPlaying}");
            }
            else
            {
                Debug.LogWarning("[MagicBlendTest] Stop() - no state found to stop.");
            }
        }

        [ContextMenu("Validate Bone Chains")]
        public void TestBoneChainValidation()
        {
            if (testBlendAsset == null || rigComponent == null) return;

            Debug.Log("[MagicBlendTest] Testing bone chain validation...");
            MagicBlendBoneChainValidator.LogAssetConfiguration(testBlendAsset);
            
            bool isValid = MagicBlendBoneChainValidator.ValidateBoneChains(testBlendAsset, rigComponent);
            Debug.Log($"[MagicBlendTest] Validation result: {(isValid ? "VALID" : "INVALID")}");
        }

        [ContextMenu("Create Upper Body Chain")]
        public void TestCreateUpperBodyChain()
        {
            if (testBlendAsset == null || rigComponent == null) return;

            Debug.Log("[MagicBlendTest] Creating upper body chain...");
            MagicBlendBoneChainValidator.CreateUpperBodyChain(
                testBlendAsset, 
                rigComponent, 
                "TestUpperBody", 
                baseWeight: 1f, 
                additiveWeight: 0.5f, 
                localWeight: 0.3f
            );
            
            Debug.Log("[MagicBlendTest] Upper body chain created. Please save the asset in the editor.");
        }

        [ContextMenu("Log Current State")]
        public void LogCurrentState()
        {
            Debug.Log("=== Current State ===");
            
            if (animancer != null)
            {
                Debug.Log($"Animancer Layers: {animancer.Layers.Count}");
                Debug.Log($"Animancer Graph Valid: {animancer.Graph.IsValidOrDispose()}");
                Debug.Log($"Animancer IsPlaying: {animancer.IsPlaying()}");
            }

            var magicBlending = GetComponent<MagicBlending>();
            if (magicBlending != null)
            {
                Debug.Log($"MagicBlending PlayableGraph Valid: {magicBlending.playableGraph.IsValid()}");
                Debug.Log($"MagicBlending Current Asset: {(magicBlending.BlendAsset ? magicBlending.BlendAsset.name : "None")}");
            }

            if (rigComponent != null)
            {
                var transforms = rigComponent.GetRigTransforms();
                Debug.Log($"Rig Transforms: {transforms.Length}");
            }

            Debug.Log("=== End State ===");
        }

        private void Update()
        {
            // Simple keyboard controls for testing
            if (Input.GetKeyDown(KeyCode.Space))
            {
                RunIntegrationTest();
            }
            
            if (Input.GetKeyDown(KeyCode.T))
            {
                TestTryPlay();
            }
            
            if (Input.GetKeyDown(KeyCode.S))
            {
                TestStop();
            }
            
            if (Input.GetKeyDown(KeyCode.V))
            {
                TestBoneChainValidation();
            }
            
            if (Input.GetKeyDown(KeyCode.L))
            {
                LogCurrentState();
            }
        }

        private void OnGUI()
        {
            if (!Application.isPlaying) return;

            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("MagicBlend + Animancer Test");
            GUILayout.Label("Controls:");
            GUILayout.Label("Space - Run Integration Test");
            GUILayout.Label("T - Test TryPlay");
            GUILayout.Label("S - Test Stop");
            GUILayout.Label("V - Validate Bone Chains");
            GUILayout.Label("L - Log Current State");
            
            if (testBlendAsset != null)
            {
                GUILayout.Label($"Test Asset: {testBlendAsset.name}");
            }
            else
            {
                GUILayout.Label("No test asset assigned!");
            }
            
            GUILayout.EndArea();
        }
    }
}
