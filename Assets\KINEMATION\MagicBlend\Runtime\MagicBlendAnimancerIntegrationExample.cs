// Designed by KINEMATION, 2025.
// Comprehensive example showing how to properly integrate MagicBlend with Animancer
// and fix common issues like upper body not being affected.

using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using KINEMATION.MagicBlend.Integration;
using KINEMATION.KAnimationCore.Runtime.Rig;
using UnityEngine;

namespace KINEMATION.MagicBlend.Examples
{
    /// <summary>
    /// Comprehensive example demonstrating proper MagicBlend + Animancer integration.
    /// Shows how to fix common issues like upper body not being affected.
    /// </summary>
    public class MagicBlendAnimancerIntegrationExample : MonoBehaviour
    {
        [Header("Required Components")]
        [SerializeField] private HybridAnimancerComponent animancer;
        [SerializeField] private MagicBlending magicBlending;
        [SerializeField] private KRigComponent rigComponent;

        [Header("MagicBlend Assets")]
        [SerializeField] private MagicBlendAsset idleBlend;
        [SerializeField] private MagicBlendAsset walkBlend;
        [SerializeField] private MagicBlendAsset upperBodyBlend;

        [Header("Settings")]
        [SerializeField] private float fadeTime = 0.25f;
        [SerializeField] private bool validateBoneChainsOnStart = true;
        [SerializeField] private bool autoFixBoneChains = true;
        [SerializeField] private bool logDetailedConfiguration = true;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = true;

        private void Start()
        {
            InitializeComponents();
            ValidateAssets();
            
            // Start with idle animation
            if (idleBlend != null)
            {
                PlayMagicBlendAsset(idleBlend, "Starting with idle blend");
            }
        }

        private void InitializeComponents()
        {
            // Auto-find components if not assigned
            if (animancer == null)
                animancer = GetComponent<HybridAnimancerComponent>();
            
            if (magicBlending == null)
                magicBlending = GetComponent<MagicBlending>();
            
            if (rigComponent == null)
                rigComponent = GetComponentInChildren<KRigComponent>();

            // Validate required components
            if (animancer == null)
            {
                Debug.LogError("[MagicBlendExample] HybridAnimancerComponent not found! MagicBlend integration requires HybridAnimancerComponent.");
                return;
            }

            if (rigComponent == null)
            {
                Debug.LogError("[MagicBlendExample] KRigComponent not found! MagicBlend requires a properly configured rig.");
                return;
            }

            // Ensure MagicBlending component exists and is initialized
            if (magicBlending == null)
            {
                magicBlending = gameObject.AddComponent<MagicBlending>();
                Debug.Log("[MagicBlendExample] Added MagicBlending component.");
            }

            // Initialize MagicBlending for Animancer
            magicBlending.InitializeForAnimancer(animancer);
            
            if (enableDebugLogs)
            {
                Debug.Log("[MagicBlendExample] Components initialized successfully.");
            }
        }

        private void ValidateAssets()
        {
            if (!validateBoneChainsOnStart) return;

            var assets = new MagicBlendAsset[] { idleBlend, walkBlend, upperBodyBlend };
            
            foreach (var asset in assets)
            {
                if (asset == null) continue;

                if (logDetailedConfiguration)
                {
                    MagicBlendBoneChainValidator.LogAssetConfiguration(asset);
                }

                bool isValid = MagicBlendBoneChainValidator.ValidateBoneChains(asset, rigComponent);
                
                if (!isValid)
                {
                    Debug.LogWarning($"[MagicBlendExample] Asset '{asset.name}' has bone chain issues!");
                    
                    if (autoFixBoneChains)
                    {
                        Debug.Log($"[MagicBlendExample] Attempting to auto-fix bone chains for '{asset.name}'");
                        bool _fixed = MagicBlendBoneChainValidator.AutoFixBoneChains(asset, rigComponent);
                        
                        if (_fixed)
                        {
                            Debug.Log($"[MagicBlendExample] Auto-fix applied to '{asset.name}'. Please save the asset in the editor.");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Plays a MagicBlendAsset with proper validation and debugging
        /// </summary>
        public void PlayMagicBlendAsset(MagicBlendAsset asset, string context = "")
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendExample] Cannot play null MagicBlendAsset.");
                return;
            }

            if (animancer == null)
            {
                Debug.LogError("[MagicBlendExample] HybridAnimancerComponent not available.");
                return;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[MagicBlendExample] Playing '{asset.name}' - {context}");
            }

            // Validate bone chains before playing
            bool isValid = MagicBlendBoneChainValidator.ValidateBoneChains(asset, rigComponent);
            if (!isValid)
            {
                Debug.LogWarning($"[MagicBlendExample] Playing '{asset.name}' with invalid bone chains. Upper body may not be affected!");
            }

            // Play using the extension method
            var state = animancer.Play(asset, fadeTime);
            
            if (enableDebugLogs)
            {
                Debug.Log($"[MagicBlendExample] Successfully played '{asset.name}'. State: {state}");
            }
        }

        /// <summary>
        /// Creates a proper upper body chain for a MagicBlendAsset
        /// Call this method to fix assets where upper body is not affected
        /// </summary>
        public void CreateUpperBodyChainForAsset(MagicBlendAsset asset)
        {
            if (asset == null || rigComponent == null)
            {
                Debug.LogError("[MagicBlendExample] Cannot create upper body chain - asset or rig component is null.");
                return;
            }

            Debug.Log($"[MagicBlendExample] Creating upper body chain for '{asset.name}'");
            
            // Create upper body chain with appropriate weights
            MagicBlendBoneChainValidator.CreateUpperBodyChain(
                asset, 
                rigComponent, 
                "UpperBody_AutoGenerated", 
                baseWeight: 1f,      // Full base weight for upper body
                additiveWeight: 0.5f, // Moderate additive weight
                localWeight: 0.3f     // Some local weight for fine control
            );

            Debug.Log($"[MagicBlendExample] Upper body chain created for '{asset.name}'. Please save the asset in the editor.");
        }

        #region Public Methods for Testing

        public void PlayIdle()
        {
            PlayMagicBlendAsset(idleBlend, "User requested idle");
        }

        public void PlayWalk()
        {
            PlayMagicBlendAsset(walkBlend, "User requested walk");
        }

        public void PlayUpperBody()
        {
            PlayMagicBlendAsset(upperBodyBlend, "User requested upper body blend");
        }

        public void StopAllAnimations()
        {
            if (animancer != null)
            {
                animancer.Stop();
                Debug.Log("[MagicBlendExample] Stopped all animations.");
            }
        }

        public void ValidateAllAssets()
        {
            Debug.Log("[MagicBlendExample] === Manual Asset Validation ===");
            ValidateAssets();
        }

        public void FixUpperBodyForAllAssets()
        {
            Debug.Log("[MagicBlendExample] === Creating Upper Body Chains for All Assets ===");
            
            var assets = new MagicBlendAsset[] { idleBlend, walkBlend, upperBodyBlend };
            foreach (var asset in assets)
            {
                if (asset != null)
                {
                    CreateUpperBodyChainForAsset(asset);
                }
            }
        }

        public void LogCurrentConfiguration()
        {
            Debug.Log("[MagicBlendExample] === Current Configuration ===");
            
            if (animancer != null)
            {
                Debug.Log($"Animancer Layers: {animancer.Layers.Count}");
                Debug.Log($"Animancer Graph Valid: {animancer.Graph.IsValidOrDispose()}");
            }

            if (magicBlending != null)
            {
                Debug.Log($"MagicBlending Initialized: {magicBlending.playableGraph.IsValid()}");
                Debug.Log($"Current Blend Asset: {(magicBlending.BlendAsset ? magicBlending.BlendAsset.name : "None")}");
            }

            if (rigComponent != null)
            {
                var transforms = rigComponent.GetRigTransforms();
                Debug.Log($"Rig Transforms: {transforms.Length}");
            }
        }

        #endregion

        #region Editor Helpers

#if UNITY_EDITOR
        [ContextMenu("Validate Bone Chains")]
        private void EditorValidateBoneChains()
        {
            if (Application.isPlaying)
            {
                ValidateAllAssets();
            }
            else
            {
                Debug.LogWarning("[MagicBlendExample] Bone chain validation requires Play mode.");
            }
        }

        [ContextMenu("Fix Upper Body Chains")]
        private void EditorFixUpperBodyChains()
        {
            if (Application.isPlaying)
            {
                FixUpperBodyForAllAssets();
            }
            else
            {
                Debug.LogWarning("[MagicBlendExample] Upper body chain creation requires Play mode.");
            }
        }

        [ContextMenu("Log Configuration")]
        private void EditorLogConfiguration()
        {
            if (Application.isPlaying)
            {
                LogCurrentConfiguration();
            }
            else
            {
                Debug.LogWarning("[MagicBlendExample] Configuration logging requires Play mode.");
            }
        }
#endif

        #endregion
    }
}
