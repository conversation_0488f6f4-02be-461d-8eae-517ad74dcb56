// Designed by KINEMATION, 2025.
// Utility class to validate and fix bone chain configurations for MagicBlend assets

using KINEMATION.KAnimationCore.Runtime.Rig;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace KINEMATION.MagicBlend.Runtime
{
    /// <summary>
    /// Utility class to validate and fix bone chain configurations for MagicBlend assets.
    /// Helps ensure that all necessary bones (including twist bones) are included in the chains.
    /// </summary>
    public static class MagicBlendBoneChainValidator
    {
        /// <summary>
        /// Common upper body bone name patterns that should be included in upper body chains
        /// </summary>
        private static readonly string[] UpperBodyBonePatterns = new string[]
        {
            "spine", "chest", "shoulder", "clavicle", "arm", "forearm", "hand", "finger", "thumb",
            "head", "neck", "twist", "roll", "upperarm", "lowerarm", "wrist"
        };

        /// <summary>
        /// Common lower body bone name patterns
        /// </summary>
        private static readonly string[] LowerBodyBonePatterns = new string[]
        {
            "pelvis", "hip", "thigh", "leg", "knee", "shin", "foot", "toe", "ankle", "calf"
        };

        /// <summary>
        /// Validates a MagicBlendAsset's bone chain configuration and reports issues
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to validate</param>
        /// <param name="rigComponent">The KRigComponent to validate against</param>
        /// <returns>True if validation passes, false if issues are found</returns>
        public static bool ValidateBoneChains(MagicBlendAsset asset, KRigComponent rigComponent)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendBoneChainValidator] Cannot validate null MagicBlendAsset.");
                return false;
            }

            if (rigComponent == null)
            {
                Debug.LogError("[MagicBlendBoneChainValidator] Cannot validate without KRigComponent.");
                return false;
            }

            bool isValid = true;
            var rigTransforms = rigComponent.GetRigTransforms();
            var rigBoneNames = rigTransforms.Where(t => t != null).Select(t => t.name).ToHashSet();

            Debug.Log($"[MagicBlendBoneChainValidator] Validating '{asset.name}' against rig with {rigBoneNames.Count} bones.");

            if (asset.layeredBlends == null || asset.layeredBlends.Count == 0)
            {
                Debug.LogError($"[MagicBlendBoneChainValidator] '{asset.name}' has no layered blends configured!");
                return false;
            }

            foreach (var blend in asset.layeredBlends)
            {
                if (blend.layer.elementChain == null || blend.layer.elementChain.Count == 0)
                {
                    Debug.LogWarning($"[MagicBlendBoneChainValidator] Blend layer '{blend.layer.chainName}' has no bones configured.");
                    isValid = false;
                    continue;
                }

                // Check for missing bones in the rig
                var missingBones = new List<string>();
                var upperBodyBones = new List<string>();
                var lowerBodyBones = new List<string>();

                foreach (var element in blend.layer.elementChain)
                {
                    if (!rigBoneNames.Contains(element.name))
                    {
                        missingBones.Add(element.name);
                    }

                    // Categorize bones
                    string boneName = element.name.ToLower();
                    if (UpperBodyBonePatterns.Any(pattern => boneName.Contains(pattern)))
                    {
                        upperBodyBones.Add(element.name);
                    }
                    else if (LowerBodyBonePatterns.Any(pattern => boneName.Contains(pattern)))
                    {
                        lowerBodyBones.Add(element.name);
                    }
                }

                // Report findings
                Debug.Log($"[MagicBlendBoneChainValidator] Layer '{blend.layer.chainName}': {blend.layer.elementChain.Count} total bones, {upperBodyBones.Count} upper body, {lowerBodyBones.Count} lower body");

                if (missingBones.Count > 0)
                {
                    Debug.LogError($"[MagicBlendBoneChainValidator] Layer '{blend.layer.chainName}' references {missingBones.Count} bones not found in rig: {string.Join(", ", missingBones)}");
                    isValid = false;
                }

                // Check for missing twist bones
                var twistBones = FindMissingTwistBones(rigBoneNames, blend.layer.elementChain.Select(e => e.name).ToList());
                if (twistBones.Count > 0)
                {
                    Debug.LogWarning($"[MagicBlendBoneChainValidator] Layer '{blend.layer.chainName}' may be missing twist bones: {string.Join(", ", twistBones)}");
                }
            }

            return isValid;
        }

        /// <summary>
        /// Finds potential twist bones that are missing from the bone chain
        /// </summary>
        private static List<string> FindMissingTwistBones(HashSet<string> rigBoneNames, List<string> chainBoneNames)
        {
            var missingTwistBones = new List<string>();
            var chainBoneSet = chainBoneNames.ToHashSet();

            foreach (var rigBone in rigBoneNames)
            {
                string boneName = rigBone.ToLower();
                if ((boneName.Contains("twist") || boneName.Contains("roll")) && !chainBoneSet.Contains(rigBone))
                {
                    missingTwistBones.Add(rigBone);
                }
            }

            return missingTwistBones;
        }

        /// <summary>
        /// Attempts to auto-fix bone chain issues by adding missing bones
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to fix</param>
        /// <param name="rigComponent">The KRigComponent to reference</param>
        /// <param name="addMissingTwistBones">Whether to add missing twist bones</param>
        /// <returns>True if any fixes were applied</returns>
        public static bool AutoFixBoneChains(MagicBlendAsset asset, KRigComponent rigComponent, bool addMissingTwistBones = true)
        {
            if (asset == null || rigComponent == null)
            {
                Debug.LogError("[MagicBlendBoneChainValidator] Cannot auto-fix with null parameters.");
                return false;
            }

            bool madeChanges = false;
            var rigTransforms = rigComponent.GetHierarchy();
            var rigBoneNames = rigTransforms.Where(t => t != null).Select(t => t.name).ToHashSet();

            foreach (var blend in asset.layeredBlends)
            {
                if (blend.layer.elementChain == null) continue;

                var chainBoneNames = blend.layer.elementChain.Select(e => e.name).ToHashSet();

                if (addMissingTwistBones)
                {
                    var missingTwistBones = FindMissingTwistBones(rigBoneNames, chainBoneNames.ToList());
                    foreach (var twistBone in missingTwistBones)
                    {
                        // Find the corresponding rig element
                        var rigElement = rigComponent.GetRigTransforms().FirstOrDefault(e => e.name == twistBone);
                        if (rigElement != null)
                        {
                            blend.layer.elementChain.Add(rigElement.GetComponent<KRigElement>());
                            Debug.Log($"[MagicBlendBoneChainValidator] Added missing twist bone '{twistBone}' to layer '{blend.layer.chainName}'");
                            madeChanges = true;
                        }
                    }
                }
            }

            if (madeChanges)
            {
                Debug.Log($"[MagicBlendBoneChainValidator] Auto-fix completed for '{asset.name}'. Please save the asset.");
            }

            return madeChanges;
        }

        /// <summary>
        /// Creates a comprehensive upper body bone chain for a MagicBlendAsset
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to modify</param>
        /// <param name="rigComponent">The KRigComponent to reference</param>
        /// <param name="chainName">Name for the new chain</param>
        /// <param name="baseWeight">Base weight for the chain</param>
        /// <param name="additiveWeight">Additive weight for the chain</param>
        /// <param name="localWeight">Local weight for the chain</param>
        public static void CreateUpperBodyChain(MagicBlendAsset asset, KRigComponent rigComponent, 
            string chainName = "UpperBody", float baseWeight = 1f, float additiveWeight = 0f, float localWeight = 0f)
        {
            if (asset == null || rigComponent == null) return;

            var rigHierarchy = rigComponent.GetHierarchy();
            var upperBodyElements = new List<KRigElement>();

            foreach (var element in rigHierarchy)
            {
                string boneName = element.name.ToLower();
                if (UpperBodyBonePatterns.Any(pattern => boneName.Contains(pattern)))
                {
                    upperBodyElements.Add(element.GetComponent<KRigElement>());
                }
            }

            if (upperBodyElements.Count > 0)
            {
                var newChain = new KRigElementChain
                {
                    chainName = chainName,
                    elementChain = upperBodyElements
                };

                var newBlend = new LayeredBlend
                {
                    layer = newChain,
                    baseWeight = baseWeight,
                    additiveWeight = additiveWeight,
                    localWeight = localWeight
                };

                if (asset.layeredBlends == null)
                    asset.layeredBlends = new List<LayeredBlend>();

                asset.layeredBlends.Add(newBlend);

                Debug.Log($"[MagicBlendBoneChainValidator] Created upper body chain '{chainName}' with {upperBodyElements.Count} bones for '{asset.name}'");
            }
        }

        /// <summary>
        /// Logs detailed information about a MagicBlendAsset's configuration
        /// </summary>
        public static void LogAssetConfiguration(MagicBlendAsset asset)
        {
            if (asset == null) return;

            Debug.Log($"=== MagicBlendAsset '{asset.name}' Configuration ===");
            Debug.Log($"Global Weight: {asset.globalWeight:F2}, Blend Time: {asset.blendTime:F2}");
            Debug.Log($"Base Pose: {(asset.basePose ? asset.basePose.name : "None")}");
            Debug.Log($"Overlay Pose: {(asset.overlayPose ? asset.overlayPose.name : "None")}");
            Debug.Log($"Is Animation: {asset.isAnimation}, Overlay Speed: {asset.overlaySpeed:F2}");

            if (asset.layeredBlends != null)
            {
                Debug.Log($"Layered Blends: {asset.layeredBlends.Count}");
                for (int i = 0; i < asset.layeredBlends.Count; i++)
                {
                    var blend = asset.layeredBlends[i];
                    int boneCount = blend.layer.elementChain?.Count ?? 0;
                    Debug.Log($"  [{i}] '{blend.layer.chainName}': {boneCount} bones, Weights(B:{blend.baseWeight:F2}, A:{blend.additiveWeight:F2}, L:{blend.localWeight:F2})");
                }
            }
            else
            {
                Debug.Log("No layered blends configured!");
            }

            Debug.Log("=== End Configuration ===");
        }
    }
}
